#!/bin/bash

# LiveTalking Docker Build Script
# This script helps you build and run the LiveTalking Docker image

set -e

# Configuration
IMAGE_NAME="livetalking"
IMAGE_TAG="latest"
CONTAINER_NAME="livetalking-container"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== LiveTalking Docker Build Script ===${NC}"

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if NVIDIA Docker runtime is available
if ! docker info | grep -q nvidia; then
    print_warning "NVIDIA Docker runtime not detected. GPU acceleration may not work."
fi

# Build the Docker image
print_info "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

if [ $? -eq 0 ]; then
    print_info "Docker image built successfully!"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Show image size
IMAGE_SIZE=$(docker images ${IMAGE_NAME}:${IMAGE_TAG} --format "table {{.Size}}" | tail -n 1)
print_info "Image size: ${IMAGE_SIZE}"

echo -e "${GREEN}=== Build Complete ===${NC}"
echo ""
echo "To run the container, use one of the following commands:"
echo ""
echo "1. Run with GPU support (recommended):"
echo "   docker run --gpus all -it --network=host --rm --name ${CONTAINER_NAME} ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""
echo "2. Run without GPU (CPU only):"
echo "   docker run -it --network=host --rm --name ${CONTAINER_NAME} ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""
echo "3. Run in background with GPU:"
echo "   docker run --gpus all -d --network=host --name ${CONTAINER_NAME} ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""
echo "4. Run with custom command:"
echo "   docker run --gpus all -it --network=host --rm ${IMAGE_NAME}:${IMAGE_TAG} /bin/bash"
echo ""
echo "Note: Make sure to download the required models before running!"
echo "Models should be placed in the models/ directory."
