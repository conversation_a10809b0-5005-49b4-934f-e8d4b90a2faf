# LiveTalking 服务的 Docker Compose 配置
# 可以单独使用，也可以合并到现有的 docker-compose.yml 中

version: '3'
services:
  livetalking:
    image: livetalking:latest
    container_name: livetalking-app
    restart: always
    # 使用host网络模式以支持UDP端口范围
    network_mode: host
    volumes:
      - /opt/work/workspace/livetalking/models:/app/models
      - /opt/work/workspace/livetalking/data:/app/data
    environment:
      - DASHSCOPE_API_KEY=sk-d3bebc7e9a584a93a4557e9c6752f237
      - TENCENT_APPID=1251115004
      - TENCENT_SECRET_KEY=d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp
      - TENCENT_SECRET_ID=AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

# 如果要合并到现有的 docker-compose.yml 中，可以使用以下配置：
# 
# livetalking:
#   image: livetalking:latest
#   container_name: livetalking-app
#   restart: always
#   network_mode: host  # 使用host网络以支持UDP端口
#   volumes:
#     - /opt/work/workspace/livetalking/models:/app/models
#     - /opt/work/workspace/livetalking/data:/app/data
#   environment:
#     - DASHSCOPE_API_KEY=your_key_here
#     - TENCENT_APPID=your_appid_here
#     - TENCENT_SECRET_KEY=your_secret_key_here
#     - TENCENT_SECRET_ID=your_secret_id_here
#   deploy:
#     resources:
#       reservations:
#         devices:
#           - driver: nvidia
#             count: all
#             capabilities: [gpu]
