# LiveTalking 服务的 Docker Compose 配置
# 可以单独使用，也可以合并到现有的 docker-compose.yml 中

version: '3'
services:
  # 方案1：使用host网络（推荐，性能最好）
  livetalking-host:
    image: livetalking:latest
    container_name: livetalking-app-host
    restart: always
    network_mode: host
    volumes:
      - /opt/work/workspace/livetalking/models:/app/models
      - /opt/work/workspace/livetalking/data:/app/data
    environment:
      - DASHSCOPE_API_KEY=sk-d3bebc7e9a584a93a4557e9c6752f237
      - TENCENT_APPID=1251115004
      - TENCENT_SECRET_KEY=d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp
      - TENCENT_SECRET_ID=AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  # 方案2：使用端口映射（Docker Compose不支持端口范围，所以只映射必要端口）
  livetalking-bridge:
    image: livetalking:latest
    container_name: livetalking-app-bridge
    restart: always
    ports:
      - "8010:8010/tcp"
      # 注意：Docker Compose不支持UDP端口范围映射
      # 如果需要大量UDP端口，建议使用host网络模式
    volumes:
      - /opt/work/workspace/livetalking/models:/app/models
      - /opt/work/workspace/livetalking/data:/app/data
    environment:
      - DASHSCOPE_API_KEY=sk-d3bebc7e9a584a93a4557e9c6752f237
      - TENCENT_APPID=1251115004
      - TENCENT_SECRET_KEY=d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp
      - TENCENT_SECRET_ID=AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA
    networks:
      - spring_cloud_default
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

networks:
  spring_cloud_default:
    external: true

# 使用方法：
# 1. 使用host网络（推荐）：
#    docker-compose up livetalking-host
#
# 2. 使用bridge网络加入现有服务组：
#    docker-compose up livetalking-bridge
