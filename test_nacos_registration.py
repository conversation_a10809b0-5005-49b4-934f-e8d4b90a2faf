#!/usr/bin/env python3
###############################################################################
#  LiveTalking Nacos注册测试脚本
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
###############################################################################

import sys
import time
from nacos_service import init_nacos_client
from logger import logger

def test_nacos_registration():
    """测试Nacos服务注册"""
    print("=" * 60)
    print("LiveTalking Nacos服务注册测试")
    print("=" * 60)
    
    try:
        # 初始化Nacos客户端
        print("1. 初始化Nacos客户端...")
        nacos_client = init_nacos_client(
            server_addresses="127.0.0.1:8848",
            namespace="",
            username="nacos",
            password="nacos"
        )
        print("✓ Nacos客户端初始化成功")
        
        # 注册测试服务
        print("\n2. 注册测试服务...")
        service_name = "livetalking-digital-human"
        port = 8010
        metadata = {
            "service_type": "digital_human",
            "version": "1.0.0",
            "model": "wav2lip",
            "transport": "webrtc",
            "test": "true"
        }
        
        success = nacos_client.register_service(
            service_name=service_name,
            port=port,
            metadata=metadata
        )
        
        if success:
            print(f"✓ 服务注册成功: {service_name}@{nacos_client.get_local_ip()}:{port}")
            
            # 启动心跳
            print("\n3. 启动心跳检测...")
            nacos_client.start_heartbeat()
            print("✓ 心跳检测已启动")
            
            # 等待一段时间让心跳生效
            print("\n4. 等待心跳生效（10秒）...")
            for i in range(10, 0, -1):
                print(f"   倒计时: {i}秒", end='\r')
                time.sleep(1)
            print("   ✓ 心跳测试完成")
            
            # 清理资源
            print("\n5. 清理测试资源...")
            nacos_client.shutdown()
            print("✓ 测试资源清理完成")
            
            print("\n" + "=" * 60)
            print("🎉 Nacos服务注册测试成功！")
            print("请到Nacos控制台查看服务注册情况:")
            print("   URL: http://127.0.0.1:8848/nacos")
            print(f"   服务名: {service_name}")
            print("=" * 60)
            
        else:
            print("✗ 服务注册失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_nacos_registration()
    sys.exit(0 if success else 1)
