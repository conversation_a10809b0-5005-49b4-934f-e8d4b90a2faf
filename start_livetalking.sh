#!/bin/bash

# LiveTalking Docker 启动脚本
# 使用方法: ./start_livetalking.sh [build|run|stop|logs|shell]

set -e

# 配置变量
IMAGE_NAME="livetalking:latest"
CONTAINER_NAME="livetalking-app"
HOST_MODELS_DIR="/opt/work/workspace/livetalking/models"
HOST_DATA_DIR="/opt/work/workspace/livetalking/data"

# API密钥配置 (请根据实际情况修改)
DASHSCOPE_API_KEY="${DASHSCOPE_API_KEY:-your_dashscope_key_here}"
TENCENT_APPID="${TENCENT_APPID:-your_tencent_appid_here}"
TENCENT_SECRET_KEY="${TENCENT_SECRET_KEY:-your_tencent_secret_key_here}"
TENCENT_SECRET_ID="${TENCENT_SECRET_ID:-your_tencent_secret_id_here}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_blue() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 未运行或无权限访问"
        exit 1
    fi
}

# 检查NVIDIA Docker支持
check_nvidia_docker() {
    if ! docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi >/dev/null 2>&1; then
        log_warn "NVIDIA Docker 支持未正确配置，将使用CPU模式"
        GPU_FLAG=""
    else
        log_info "NVIDIA Docker 支持正常"
        GPU_FLAG="--gpus all"
    fi
}

# 检查挂载目录
check_mount_dirs() {
    if [ ! -d "$HOST_MODELS_DIR" ]; then
        log_warn "模型目录不存在: $HOST_MODELS_DIR"
        log_info "创建目录: $HOST_MODELS_DIR"
        mkdir -p "$HOST_MODELS_DIR"
    fi
    
    if [ ! -d "$HOST_DATA_DIR" ]; then
        log_warn "数据目录不存在: $HOST_DATA_DIR"
        log_info "创建目录: $HOST_DATA_DIR"
        mkdir -p "$HOST_DATA_DIR"
    fi
    
    log_info "挂载目录检查完成"
}

# 构建镜像
build_image() {
    log_info "开始构建 Docker 镜像..."
    docker build -t "$IMAGE_NAME" .
    log_info "镜像构建完成: $IMAGE_NAME"
}

# 停止并删除现有容器
stop_container() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "停止现有容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "删除现有容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
}

# 运行容器
run_container() {
    log_info "启动 LiveTalking 容器..."
    
    # 检查环境
    check_docker
    check_nvidia_docker
    check_mount_dirs
    
    # 停止现有容器
    stop_container
    
    # 启动新容器
    log_blue "启动命令:"
    echo "docker run -d --name $CONTAINER_NAME \\"
    echo "  $GPU_FLAG \\"
    echo "  --network=host \\"
    echo "  -v $HOST_MODELS_DIR:/app/models \\"
    echo "  -v $HOST_DATA_DIR:/app/data \\"
    echo "  -e DASHSCOPE_API_KEY=$DASHSCOPE_API_KEY \\"
    echo "  -e TENCENT_APPID=$TENCENT_APPID \\"
    echo "  -e TENCENT_SECRET_KEY=$TENCENT_SECRET_KEY \\"
    echo "  -e TENCENT_SECRET_ID=$TENCENT_SECRET_ID \\"
    echo "  $IMAGE_NAME"
    echo ""
    
    docker run -d --name "$CONTAINER_NAME" \
        $GPU_FLAG \
        --network=host \
        -v "$HOST_MODELS_DIR:/app/models" \
        -v "$HOST_DATA_DIR:/app/data" \
        -e DASHSCOPE_API_KEY="$DASHSCOPE_API_KEY" \
        -e TENCENT_APPID="$TENCENT_APPID" \
        -e TENCENT_SECRET_KEY="$TENCENT_SECRET_KEY" \
        -e TENCENT_SECRET_ID="$TENCENT_SECRET_ID" \
        "$IMAGE_NAME"
    
    log_info "容器启动成功: $CONTAINER_NAME"
    log_info "服务地址: http://localhost:8010"
    log_info "查看日志: ./start_livetalking.sh logs"
    log_info "进入容器: ./start_livetalking.sh shell"
}

# 查看日志
show_logs() {
    if ! docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_error "容器 $CONTAINER_NAME 未运行"
        exit 1
    fi
    
    log_info "显示容器日志 (Ctrl+C 退出):"
    docker logs -f "$CONTAINER_NAME"
}

# 进入容器shell
enter_shell() {
    if ! docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_error "容器 $CONTAINER_NAME 未运行"
        exit 1
    fi
    
    log_info "进入容器 shell:"
    docker exec -it "$CONTAINER_NAME" /bin/bash
}

# 显示状态
show_status() {
    log_info "Docker 容器状态:"
    docker ps -a --filter name="$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "服务正在运行: http://localhost:8010"
    fi
}

# 显示帮助
show_help() {
    echo "LiveTalking Docker 启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build   - 构建 Docker 镜像"
    echo "  run     - 运行容器 (默认)"
    echo "  stop    - 停止并删除容器"
    echo "  logs    - 查看容器日志"
    echo "  shell   - 进入容器 shell"
    echo "  status  - 显示容器状态"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DASHSCOPE_API_KEY    - DashScope API 密钥"
    echo "  TENCENT_APPID        - 腾讯云 App ID"
    echo "  TENCENT_SECRET_KEY   - 腾讯云 Secret Key"
    echo "  TENCENT_SECRET_ID    - 腾讯云 Secret ID"
    echo ""
    echo "示例:"
    echo "  $0 build              # 构建镜像"
    echo "  $0 run                # 运行容器"
    echo "  $0 logs               # 查看日志"
    echo "  TENCENT_APPID=123 $0 run  # 设置环境变量并运行"
}

# 主函数
main() {
    case "${1:-run}" in
        "build")
            build_image
            ;;
        "run")
            run_container
            ;;
        "stop")
            stop_container
            ;;
        "logs")
            show_logs
            ;;
        "shell")
            enter_shell
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
