#!/bin/bash

# LiveTalking 简单启动脚本

# 配置路径 (请根据实际情况修改)
HOST_MODELS_DIR="/opt/work/workspace/livetalking/models"
HOST_DATA_DIR="/opt/work/workspace/livetalking/data"

# API密钥配置 (请修改为你的实际密钥)
DASHSCOPE_API_KEY="sk-d3bebc7e9a584a93a4557e9c6752f237"
TENCENT_APPID="1251115004"
TENCENT_SECRET_KEY="d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp"
TENCENT_SECRET_ID="AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA"

# 停止现有容器
echo "停止现有容器..."
docker stop livetalking-app 2>/dev/null || true
docker rm livetalking-app 2>/dev/null || true

# 启动容器
echo "启动 LiveTalking 容器..."
docker run -d --name livetalking-app \
  --gpus all \
  --network=host \
  -v $HOST_MODELS_DIR:/app/models \
  -v $HOST_DATA_DIR:/app/data \
  -e DASHSCOPE_API_KEY=$DASHSCOPE_API_KEY \
  -e TENCENT_APPID=$TENCENT_APPID \
  -e TENCENT_SECRET_KEY=$TENCENT_SECRET_KEY \
  -e TENCENT_SECRET_ID=$TENCENT_SECRET_ID \
  livetalking:latest

echo "容器启动完成!"
echo "服务地址: http://localhost:8010"
echo "查看日志: docker logs -f livetalking-app"
