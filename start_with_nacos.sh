#!/bin/bash

###############################################################################
#  LiveTalking服务启动脚本（带Nacos注册）
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
###############################################################################

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_MODEL="wav2lip"
DEFAULT_AVATAR_ID="wav2lip256_avatar1"
DEFAULT_TRANSPORT="webrtc"
DEFAULT_PORT=8010
DEFAULT_NACOS_SERVER="127.0.0.1:8848"
DEFAULT_MAX_SESSION=1
DEFAULT_BATCH_SIZE=16

# 显示帮助信息
show_help() {
    echo "LiveTalking服务启动脚本（带Nacos注册）"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --model MODEL           数字人模型 (wav2lip|musetalk|ultralight) [默认: $DEFAULT_MODEL]"
    echo "  -a, --avatar AVATAR_ID      Avatar ID [默认: $DEFAULT_AVATAR_ID]"
    echo "  -t, --transport TRANSPORT   传输方式 (webrtc|rtcpush|virtualcam) [默认: $DEFAULT_TRANSPORT]"
    echo "  -p, --port PORT             监听端口 [默认: $DEFAULT_PORT]"
    echo "  -n, --nacos SERVER          Nacos服务器地址 [默认: $DEFAULT_NACOS_SERVER]"
    echo "  -s, --max-session COUNT     最大会话数 [默认: $DEFAULT_MAX_SESSION]"
    echo "  -b, --batch-size SIZE       批处理大小 [默认: $DEFAULT_BATCH_SIZE]"
    echo "  --no-nacos                  禁用Nacos注册"
    echo "  --tts TTS_TYPE              TTS服务类型 [默认: edgetts]"
    echo "  -h, --help                  显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -m wav2lip -a wav2lip256_avatar1 -t webrtc"
    echo "  $0 --model musetalk --avatar avator_1 --no-nacos"
    echo "  $0 -n *************:8848 -p 8011"
}

# 检查Python环境
check_python_env() {
    print_info "检查Python环境..."
    
    if [ ! -d "livetalking_env" ]; then
        print_error "未找到Python虚拟环境 livetalking_env"
        print_info "请先运行以下命令创建虚拟环境："
        echo "  python3 -m venv livetalking_env"
        echo "  source livetalking_env/bin/activate"
        echo "  pip install -r requirements.txt"
        exit 1
    fi
    
    print_success "Python虚拟环境检查通过"
}

# 检查必要文件
check_required_files() {
    print_info "检查必要文件..."
    
    local required_files=("app.py" "nacos_service.py" "nacos_config.yaml" "requirements.txt")
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "未找到必要文件: $file"
            exit 1
        fi
    done
    
    print_success "必要文件检查通过"
}

# 检查Nacos连接
check_nacos_connection() {
    local nacos_server=$1
    print_info "检查Nacos连接: $nacos_server"
    
    # 提取IP和端口
    local nacos_host=$(echo $nacos_server | cut -d':' -f1)
    local nacos_port=$(echo $nacos_server | cut -d':' -f2)
    
    # 检查连接
    if timeout 5 bash -c "</dev/tcp/$nacos_host/$nacos_port" 2>/dev/null; then
        print_success "Nacos连接检查通过"
        return 0
    else
        print_warning "无法连接到Nacos服务器: $nacos_server"
        print_warning "请确保Nacos服务正在运行"
        return 1
    fi
}

# 解析命令行参数
MODEL=$DEFAULT_MODEL
AVATAR_ID=$DEFAULT_AVATAR_ID
TRANSPORT=$DEFAULT_TRANSPORT
PORT=$DEFAULT_PORT
NACOS_SERVER=$DEFAULT_NACOS_SERVER
MAX_SESSION=$DEFAULT_MAX_SESSION
BATCH_SIZE=$DEFAULT_BATCH_SIZE
ENABLE_NACOS=true
TTS_TYPE="edgetts"

while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--model)
            MODEL="$2"
            shift 2
            ;;
        -a|--avatar)
            AVATAR_ID="$2"
            shift 2
            ;;
        -t|--transport)
            TRANSPORT="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -n|--nacos)
            NACOS_SERVER="$2"
            shift 2
            ;;
        -s|--max-session)
            MAX_SESSION="$2"
            shift 2
            ;;
        -b|--batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --no-nacos)
            ENABLE_NACOS=false
            shift
            ;;
        --tts)
            TTS_TYPE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    print_info "启动LiveTalking服务（带Nacos注册）"
    print_info "========================================"
    
    # 检查环境
    check_python_env
    check_required_files
    
    # 如果启用Nacos，检查连接
    if [ "$ENABLE_NACOS" = true ]; then
        if ! check_nacos_connection "$NACOS_SERVER"; then
            read -p "是否继续启动服务（不注册到Nacos）？[y/N]: " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_info "服务启动已取消"
                exit 1
            fi
            ENABLE_NACOS=false
        fi
    fi
    
    # 显示配置信息
    print_info "服务配置:"
    echo "  模型: $MODEL"
    echo "  Avatar ID: $AVATAR_ID"
    echo "  传输方式: $TRANSPORT"
    echo "  监听端口: $PORT"
    echo "  最大会话数: $MAX_SESSION"
    echo "  批处理大小: $BATCH_SIZE"
    echo "  TTS类型: $TTS_TYPE"
    if [ "$ENABLE_NACOS" = true ]; then
        echo "  Nacos服务器: $NACOS_SERVER"
        echo "  Nacos注册: 启用"
    else
        echo "  Nacos注册: 禁用"
    fi
    echo ""
    
    # 激活虚拟环境并启动服务
    print_info "启动服务..."
    
    # 构建启动命令
    local cmd="source livetalking_env/bin/activate && python app.py"
    cmd="$cmd --model $MODEL"
    cmd="$cmd --avatar_id $AVATAR_ID"
    cmd="$cmd --transport $TRANSPORT"
    cmd="$cmd --listenport $PORT"
    cmd="$cmd --max_session $MAX_SESSION"
    cmd="$cmd --batch_size $BATCH_SIZE"
    cmd="$cmd --tts $TTS_TYPE"
    
    if [ "$ENABLE_NACOS" = true ]; then
        cmd="$cmd --enable_nacos"
        cmd="$cmd --nacos_server $NACOS_SERVER"
    fi
    
    print_info "执行命令: $cmd"
    print_success "服务正在启动，请稍候..."
    
    # 执行命令
    eval $cmd
}

# 运行主函数
main "$@"
