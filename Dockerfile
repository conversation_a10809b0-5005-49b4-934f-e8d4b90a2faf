# LiveTalking Docker Image
# Based on NVIDIA CUDA for GPU acceleration
ARG BASE_IMAGE=swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nvidia/cuda:12.4.1-cudnn-devel-ubuntu22.04
FROM $BASE_IMAGE

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# nvidia-container-runtime
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility,graphics
ENV DASHSCOPE_API_KEY=sk-d3bebc7e9a584a93a4557e9c6752f237
ENV TENCENT_APPID=1251115004
ENV TENCENT_SECRET_KEY=d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp
ENV TENCENT_SECRET_ID=AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA
# Install system dependencies
RUN apt-get update -yq --fix-missing \
 && apt-get install -yq --no-install-recommends \
    pkg-config \
    wget \
    cmake \
    curl \
    git \
    vim \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgl1-mesa-glx \
    libfontconfig1 \
    libxrender1 \
    libgtk-3-0 \
    python3-dev \
    python3-pip \
    build-essential \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

# Install Miniconda
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
 && bash /tmp/miniconda.sh -b -p /opt/conda \
 && rm /tmp/miniconda.sh \
 && /opt/conda/bin/conda clean -ya

# Add conda to PATH
ENV PATH=/opt/conda/bin:$PATH

# Accept conda terms of service and create conda environment
RUN conda config --set channel_priority flexible \
 && conda config --add channels conda-forge \
 && conda config --set always_yes true \
 && conda create -n livetalking python=3.10 -y \
 && conda clean -ya

# Activate environment and install PyTorch
SHELL ["/bin/bash", "-c"]
RUN source activate livetalking \
 && conda install pytorch==2.5.0 torchvision==0.20.0 torchaudio==2.5.0 pytorch-cuda=12.4 -c pytorch -c nvidia -y \
 && conda clean -ya

# Set pip mirror for faster downloads
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt ./

# Install Python dependencies
RUN source activate livetalking \
 && pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy data directories (including any existing content)
COPY models/ /app/models/
COPY data/ /app/data/

# Ensure all necessary directories exist
RUN mkdir -p /app/data/customvideo

# Set HuggingFace mirror for Chinese users
ENV HF_ENDPOINT=https://hf-mirror.com

# Expose port
EXPOSE 8010

# Set default command
CMD ["/bin/bash", "-c", "source activate livetalking && python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1"]
