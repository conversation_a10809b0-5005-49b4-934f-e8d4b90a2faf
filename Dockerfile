# LiveTalking Docker Image
# Based on NVIDIA CUDA for GPU acceleration
ARG BASE_IMAGE=swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nvidia/cuda:12.4.1-cudnn-devel-ubuntu22.04
FROM $BASE_IMAGE

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# nvidia-container-runtime
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility,graphics

# API keys should be set at runtime, not in the image
# ENV DASHSCOPE_API_KEY=your_key_here
# ENV TENCENT_APPID=your_appid_here
# ENV TENCENT_SECRET_KEY=your_secret_key_here
# ENV TENCENT_SECRET_ID=your_secret_id_here
# Install system dependencies
RUN apt-get update -yq --fix-missing \
 && apt-get install -yq --no-install-recommends \
    pkg-config \
    wget \
    cmake \
    curl \
    git \
    vim \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgl1-mesa-glx \
    libfontconfig1 \
    libxrender1 \
    libgtk-3-0 \
    python3-dev \
    python3-pip \
    build-essential \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

# Install Python 3.10 and pip
RUN apt-get update \
 && apt-get install -y software-properties-common \
 && add-apt-repository ppa:deadsnakes/ppa \
 && apt-get update \
 && apt-get install -y python3.10 python3.10-venv python3.10-dev \
 && update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1 \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python3 -m venv /opt/livetalking
ENV PATH="/opt/livetalking/bin:$PATH"

# Set pip mirror for faster downloads
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# Install PyTorch from aliyun mirror
RUN pip install torch==2.5.0 torchvision==0.20.0 torchaudio==2.5.0

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional libraries (skip optional dependencies due to network/compatibility issues)
# pytorch3d and tensorflow-gpu are optional for basic functionality
# RUN pip install "git+https://github.com/facebookresearch/pytorch3d.git"
# RUN pip install tensorflow-gpu==2.8.0
RUN pip uninstall protobuf -y || true \
 && pip install protobuf==3.20.1

# Copy application code
COPY . .

# Copy data directories (including any existing content)
# Note: models and data directories will be mounted at runtime
# COPY models/ /app/models/
# COPY data/ /app/data/

# Ensure all necessary directories exist
RUN mkdir -p /app/data/customvideo

# Set HuggingFace mirror for Chinese users
ENV HF_ENDPOINT=https://hf-mirror.com

# Expose port
EXPOSE 8010

# Set default command
CMD ["python", "app.py", "--transport", "webrtc", "--model", "wav2lip", "--avatar_id", "katong_shuchu_256", "--asrtype", "tencent", "--customvideo_config", "data/custom_config_katong.json"]
