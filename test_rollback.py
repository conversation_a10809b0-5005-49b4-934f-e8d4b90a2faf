#!/usr/bin/env python3
###############################################################################
#  代码回滚验证测试脚本
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
###############################################################################

import os
import sys

def test_rollback():
    """验证代码回滚是否成功"""
    print("=" * 60)
    print("LiveTalking代码回滚验证")
    print("=" * 60)
    
    # 检查已删除的配置化文件
    removed_files = [
        "tts_config_manager.py",
        "config_manager.py", 
        "config.yaml",
        "test_tts_config_api.py",
        "TTS_CONFIG_API.md"
    ]
    
    print("1. 检查已删除的配置化文件...")
    for file in removed_files:
        if os.path.exists(file):
            print(f"❌ 文件仍存在: {file}")
        else:
            print(f"✅ 文件已删除: {file}")
    
    # 检查保留的Nacos文件
    nacos_files = [
        "nacos_service.py",
        "nacos_config.yaml"
    ]
    
    print("\n2. 检查保留的Nacos文件...")
    for file in nacos_files:
        if os.path.exists(file):
            print(f"✅ Nacos文件保留: {file}")
        else:
            print(f"❌ Nacos文件丢失: {file}")
    
    # 检查TTS实现是否回滚
    print("\n3. 检查TTS实现是否回滚...")
    try:
        with open("ttsreal.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 检查是否还有配置化相关代码
        if "tts_config_manager" in content:
            print("❌ ttsreal.py中仍有配置化代码")
        else:
            print("✅ ttsreal.py配置化代码已清理")
            
        # 检查EdgeTTS是否回滚
        if "self.opt.REF_FILE" in content:
            print("✅ EdgeTTS已回滚到使用self.opt.REF_FILE")
        else:
            print("❌ EdgeTTS未正确回滚")
            
        # 检查TencentTTS是否回滚
        if 'os.getenv("TENCENT_APPID")' in content:
            print("✅ TencentTTS已回滚到使用环境变量")
        else:
            print("❌ TencentTTS未正确回滚")
            
    except Exception as e:
        print(f"❌ 检查ttsreal.py失败: {e}")
    
    # 检查app.py中的API是否清理
    print("\n4. 检查app.py中的配置API是否清理...")
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "/api/tts/config" in content:
            print("❌ app.py中仍有TTS配置API")
        else:
            print("✅ app.py中TTS配置API已清理")
            
        # 检查Nacos功能是否保留
        if "register_to_nacos" in content:
            print("✅ app.py中Nacos功能已保留")
        else:
            print("❌ app.py中Nacos功能丢失")
            
    except Exception as e:
        print(f"❌ 检查app.py失败: {e}")
    
    # 检查requirements.txt
    print("\n5. 检查requirements.txt...")
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "nacos-sdk-python" in content:
            print("✅ requirements.txt中Nacos依赖已保留")
        else:
            print("❌ requirements.txt中Nacos依赖丢失")
            
        if "pyyaml" in content:
            print("❌ requirements.txt中仍有多余的pyyaml依赖")
        else:
            print("✅ requirements.txt中多余依赖已清理")
            
    except Exception as e:
        print(f"❌ 检查requirements.txt失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 代码回滚验证完成！")
    print("✅ 保留功能: Nacos服务注册")
    print("❌ 已回滚: TTS配置化功能")
    print("💡 现在可以使用原有的启动参数方式配置TTS")
    print("=" * 60)

if __name__ == "__main__":
    test_rollback()
