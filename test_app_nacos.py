#!/usr/bin/env python3
###############################################################################
#  LiveTalking应用启动测试脚本（带Nacos注册）
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
###############################################################################

import sys
import time
import asyncio
import aiohttp
from nacos_service import init_nacos_client
from logger import logger

async def test_service_startup():
    """测试服务启动和Nacos注册"""
    print("=" * 60)
    print("LiveTalking服务启动测试（带Nacos注册）")
    print("=" * 60)
    
    try:
        # 1. 测试Nacos连接
        print("1. 测试Nacos连接...")
        nacos_client = init_nacos_client(
            server_addresses="127.0.0.1:8848",
            namespace="",
            username="nacos",
            password="nacos"
        )
        print("✓ Nacos连接成功")
        
        # 2. 注册服务
        print("\n2. 注册LiveTalking服务...")
        service_name = "livetalking-digital-human"
        port = 8010
        metadata = {
            "service_type": "digital_human",
            "version": "1.0.0",
            "model": "wav2lip",
            "transport": "webrtc",
            "avatar_id": "wav2lip256_avatar1",
            "tts": "edgetts",
            "max_sessions": "1",
            "batch_size": "16"
        }
        
        success = nacos_client.register_service(
            service_name=service_name,
            port=port,
            metadata=metadata
        )
        
        if success:
            print(f"✓ 服务注册成功: {service_name}@{nacos_client.get_local_ip()}:{port}")
            
            # 3. 启动心跳
            print("\n3. 启动心跳检测...")
            nacos_client.start_heartbeat()
            print("✓ 心跳检测已启动")
            
            # 4. 模拟健康检查
            print("\n4. 测试健康检查端点...")
            health_url = f"http://127.0.0.1:{port}/health"
            
            # 等待一下让服务完全启动
            print("   等待服务启动...")
            await asyncio.sleep(3)
            
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(health_url, timeout=5) as response:
                        if response.status == 200:
                            health_data = await response.json()
                            print(f"✓ 健康检查通过: {health_data}")
                        else:
                            print(f"⚠ 健康检查返回状态码: {response.status}")
            except Exception as e:
                print(f"⚠ 健康检查失败（服务可能未完全启动）: {e}")
            
            # 5. 保持服务运行一段时间
            print("\n5. 保持服务运行（30秒）...")
            for i in range(30, 0, -1):
                print(f"   剩余时间: {i}秒", end='\r')
                await asyncio.sleep(1)
            print("   ✓ 服务运行测试完成")
            
            # 6. 清理资源
            print("\n6. 清理测试资源...")
            nacos_client.shutdown()
            print("✓ 测试资源清理完成")
            
            print("\n" + "=" * 60)
            print("🎉 LiveTalking服务启动测试成功！")
            print("请到Nacos控制台查看服务注册情况:")
            print("   URL: http://127.0.0.1:8848/nacos")
            print(f"   服务名: {service_name}")
            print("   用户名: nacos")
            print("   密码: nacos")
            print("=" * 60)
            
        else:
            print("✗ 服务注册失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_service_startup())
    sys.exit(0 if success else 1)
