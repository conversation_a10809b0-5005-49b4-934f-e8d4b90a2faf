# LLM配置文件
llm:
  # 默认使用的LLM提供商
  default_provider: "dashscope"
  
  # 各种LLM提供商配置
  providers:
    # 阿里云DashScope (通义千问)
    dashscope:
      enabled: true
      api_key_env: "DASHSCOPE_API_KEY"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      models:
        - name: "qwen-plus"
          display_name: "通义千问Plus"
          max_tokens: 8192
          temperature: 0.7
        - name: "qwen-turbo"
          display_name: "通义千问Turbo"
          max_tokens: 8192
          temperature: 0.7
        - name: "qwen-max"
          display_name: "通义千问Max"
          max_tokens: 8192
          temperature: 0.7
      default_model: "qwen-plus"
    
    # OpenAI
    openai:
      enabled: true
      api_key_env: "OPENAI_API_KEY"
      base_url: "https://api.openai.com/v1"
      models:
        - name: "gpt-3.5-turbo"
          display_name: "GPT-3.5 Turbo"
          max_tokens: 4096
          temperature: 0.7
        - name: "gpt-4"
          display_name: "GPT-4"
          max_tokens: 8192
          temperature: 0.7
        - name: "gpt-4-turbo"
          display_name: "GPT-4 Turbo"
          max_tokens: 128000
          temperature: 0.7
      default_model: "gpt-3.5-turbo"
    
    # 智谱AI (GLM)
    zhipuai:
      enabled: true
      api_key_env: "ZHIPUAI_API_KEY"
      base_url: "https://open.bigmodel.cn/api/paas/v4"
      models:
        - name: "glm-4"
          display_name: "GLM-4"
          max_tokens: 8192
          temperature: 0.7
        - name: "glm-3-turbo"
          display_name: "GLM-3 Turbo"
          max_tokens: 8192
          temperature: 0.7
      default_model: "glm-4"
    
    # 百度文心一言
    wenxin:
      enabled: true
      api_key_env: "WENXIN_API_KEY"
      secret_key_env: "WENXIN_SECRET_KEY"
      base_url: "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat"
      models:
        - name: "ernie-bot"
          display_name: "文心一言"
          max_tokens: 8192
          temperature: 0.7
        - name: "ernie-bot-turbo"
          display_name: "文心一言Turbo"
          max_tokens: 8192
          temperature: 0.7
      default_model: "ernie-bot"
    
    # 本地模型 (Ollama)
    ollama:
      enabled: false
      base_url: "http://localhost:11434/v1"
      models:
        - name: "llama2"
          display_name: "Llama 2"
          max_tokens: 4096
          temperature: 0.7
        - name: "qwen:7b"
          display_name: "Qwen 7B"
          max_tokens: 8192
          temperature: 0.7
      default_model: "llama2"

  # 对话配置
  conversation:
    # 系统提示词
    system_prompt: "You are a helpful assistant. Please respond in a natural and conversational way."
    
    # 流式输出配置
    stream: true
    
    # 响应参数
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
    
    # 对话历史管理
    max_history: 10
    
    # 文本分割配置（用于实时TTS）
    text_split:
      enabled: true
      min_length: 10
      delimiters: ",.!;:，。！？：；"
