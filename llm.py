import time
import os
from basereal import BaseReal
from logger import logger
from config_manager import get_config_manager

def llm_response(message,nerfreal:BaseReal):
    start = time.perf_counter()
    from openai import OpenAI

    # 从配置管理器获取LLM配置
    config_manager = get_config_manager()
    if config_manager:
        llm_config = config_manager.get_llm_config()
        api_key = llm_config.get('api_key')
        base_url = llm_config.get('base_url')
        model = llm_config.get('model', 'qwen-plus')
    else:
        # 降级到环境变量
        api_key = os.getenv("DASHSCOPE_API_KEY")
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        model = "qwen-plus"

    if not api_key:
        logger.error("LLM API Key未配置，请设置DASHSCOPE_API_KEY环境变量或在配置文件中设置")
        nerfreal.put_msg_txt("抱歉，语言模型配置有误，无法回答您的问题。")
        return

    client = OpenAI(
        api_key=api_key,
        base_url=base_url,
    )
    end = time.perf_counter()
    logger.info(f"llm Time init: {end-start}s")
    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=[{'role': 'system', 'content': 'You are a helpful assistant.'},
                  {'role': 'user', 'content': message}],
        stream=True,
        # 通过以下设置，在流式输出的最后一行展示token使用信息
        stream_options={"include_usage": True}
    )
    result=""
    first = True
    for chunk in completion:
        if len(chunk.choices)>0:
            #print(chunk.choices[0].delta.content)
            if first:
                end = time.perf_counter()
                logger.info(f"llm Time to first chunk: {end-start}s")
                first = False
            msg = chunk.choices[0].delta.content
            lastpos=0
            #msglist = re.split('[,.!;:，。！?]',msg)
            for i, char in enumerate(msg):
                if char in ",.!;:，。！？：；" :
                    result = result+msg[lastpos:i+1]
                    lastpos = i+1
                    if len(result)>10:
                        logger.info(result)
                        nerfreal.put_msg_txt(result)
                        result=""
            result = result+msg[lastpos:]
    end = time.perf_counter()
    logger.info(f"llm Time to last chunk: {end-start}s")
    nerfreal.put_msg_txt(result)    