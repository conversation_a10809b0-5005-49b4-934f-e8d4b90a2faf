# Nacos配置文件
nacos:
  # Nacos服务器地址
  server_addresses: "127.0.0.1:8848"

  # 命名空间ID（留空表示使用public命名空间）
  namespace: ""

  # 认证信息（Nacos启用了认证）
  username: "nacos"
  password: "nacos"
  
  # 服务配置
  service:
    # 服务名称（与Java服务保持一致的命名风格）
    name: "livetalking-digital-human"

    # 服务分组
    group: "DEFAULT_GROUP"
    
    # 集群名称
    cluster: "DEFAULT"
    
    # 服务权重
    weight: 1.0
    
    # 服务元数据
    metadata:
      service_type: "digital_human"
      version: "1.0.0"
      description: "LiveTalking实时交互流式数字人服务"
      models: "wav2lip,musetalk,ultralight"
      transport: "webrtc"
      
  # 心跳配置
  heartbeat:
    # 是否启用心跳
    enabled: true
    
    # 心跳间隔（秒）
    interval: 30
    
  # 健康检查配置
  health_check:
    # 健康检查URL路径
    path: "/health"
    
    # 健康检查间隔（秒）
    interval: 10
    
    # 健康检查超时时间（秒）
    timeout: 3
