###############################################################################
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
#  email: <EMAIL>
# 
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#  
#       http://www.apache.org/licenses/LICENSE-2.0
# 
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
###############################################################################

import nacos
import socket
import threading
import time
import json
from logger import logger
from typing import Optional, Dict, Any


class NacosServiceRegistry:
    """Nacos服务注册与发现客户端"""
    
    def __init__(self, server_addresses: str = "127.0.0.1:8848", 
                 namespace: str = "", username: str = "", password: str = ""):
        """
        初始化Nacos客户端
        
        Args:
            server_addresses: Nacos服务器地址，默认127.0.0.1:8848
            namespace: 命名空间ID，默认为空（public命名空间）
            username: 用户名（如果启用了认证）
            password: 密码（如果启用了认证）
        """
        self.server_addresses = server_addresses
        self.namespace = namespace
        self.username = username
        self.password = password
        self.client = None
        self.registered_services = {}
        self.heartbeat_thread = None
        self.running = False
        
        self._init_client()
    
    def _init_client(self):
        """初始化Nacos客户端"""
        try:
            # 使用正确的参数名称初始化客户端
            self.client = nacos.NacosClient(
                server_addresses=self.server_addresses,
                namespace=self.namespace if self.namespace else None,
                username=self.username if self.username else None,
                password=self.password if self.password else None
            )
            logger.info(f"Nacos客户端初始化成功，服务器地址: {self.server_addresses}")
            
        except Exception as e:
            logger.error(f"Nacos客户端初始化失败: {e}")
            raise
    
    def get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 创建一个UDP socket来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def register_service(self, service_name: str, port: int, 
                        ip: Optional[str] = None, 
                        metadata: Optional[Dict[str, Any]] = None,
                        group_name: str = "DEFAULT_GROUP",
                        cluster_name: str = "DEFAULT",
                        weight: float = 1.0,
                        enable: bool = True,
                        healthy: bool = True) -> bool:
        """
        注册服务到Nacos
        
        Args:
            service_name: 服务名称
            port: 服务端口
            ip: 服务IP，如果不提供则自动获取本机IP
            metadata: 服务元数据
            group_name: 服务分组
            cluster_name: 集群名称
            weight: 权重
            enable: 是否启用
            healthy: 是否健康
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if ip is None:
                ip = self.get_local_ip()
            
            if metadata is None:
                metadata = {}
            
            # 添加默认元数据
            metadata.update({
                "service_type": "livetalking",
                "version": "1.0.0",
                "register_time": str(int(time.time()))
            })
            
            # 注册服务
            result = self.client.add_naming_instance(
                service_name=service_name,
                ip=ip,
                port=port,
                cluster_name=cluster_name,
                weight=weight,
                metadata=metadata,
                enable=enable,
                healthy=healthy,
                group_name=group_name
            )
            
            if result:
                service_key = f"{group_name}@@{service_name}"
                self.registered_services[service_key] = {
                    "service_name": service_name,
                    "ip": ip,
                    "port": port,
                    "group_name": group_name,
                    "cluster_name": cluster_name,
                    "metadata": metadata
                }
                logger.info(f"服务注册成功: {service_name}@{ip}:{port}")
                return True
            else:
                logger.error(f"服务注册失败: {service_name}@{ip}:{port}")
                return False
                
        except Exception as e:
            logger.error(f"服务注册异常: {e}")
            return False
    
    def deregister_service(self, service_name: str, ip: str, port: int,
                          group_name: str = "DEFAULT_GROUP",
                          cluster_name: str = "DEFAULT") -> bool:
        """
        从Nacos注销服务
        
        Args:
            service_name: 服务名称
            ip: 服务IP
            port: 服务端口
            group_name: 服务分组
            cluster_name: 集群名称
            
        Returns:
            bool: 注销是否成功
        """
        try:
            result = self.client.remove_naming_instance(
                service_name=service_name,
                ip=ip,
                port=port,
                cluster_name=cluster_name,
                group_name=group_name
            )
            
            if result:
                service_key = f"{group_name}@@{service_name}"
                if service_key in self.registered_services:
                    del self.registered_services[service_key]
                logger.info(f"服务注销成功: {service_name}@{ip}:{port}")
                return True
            else:
                logger.error(f"服务注销失败: {service_name}@{ip}:{port}")
                return False
                
        except Exception as e:
            logger.error(f"服务注销异常: {e}")
            return False
    
    def discover_service(self, service_name: str, 
                        group_name: str = "DEFAULT_GROUP",
                        clusters: Optional[str] = None,
                        healthy_only: bool = True) -> list:
        """
        发现服务实例
        
        Args:
            service_name: 服务名称
            group_name: 服务分组
            clusters: 集群名称列表，用逗号分隔
            healthy_only: 是否只返回健康的实例
            
        Returns:
            list: 服务实例列表
        """
        try:
            instances = self.client.list_naming_instance(
                service_name=service_name,
                group_name=group_name,
                clusters=clusters,
                healthy_only=healthy_only
            )
            
            logger.info(f"发现服务实例: {service_name}, 实例数量: {len(instances)}")
            return instances
            
        except Exception as e:
            logger.error(f"服务发现异常: {e}")
            return []
    
    def start_heartbeat(self):
        """启动心跳检测"""
        if self.heartbeat_thread is not None and self.heartbeat_thread.is_alive():
            return
        
        self.running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        self.heartbeat_thread.start()
        logger.info("Nacos心跳检测已启动")
    
    def stop_heartbeat(self):
        """停止心跳检测"""
        self.running = False
        if self.heartbeat_thread is not None:
            self.heartbeat_thread.join(timeout=5)
        logger.info("Nacos心跳检测已停止")
    
    def _heartbeat_worker(self):
        """心跳检测工作线程"""
        while self.running:
            try:
                for service_key, service_info in self.registered_services.items():
                    # 发送心跳
                    self.client.send_heartbeat(
                        service_name=service_info["service_name"],
                        ip=service_info["ip"],
                        port=service_info["port"],
                        group_name=service_info.get("group_name", "DEFAULT_GROUP"),
                        cluster_name=service_info.get("cluster_name", "DEFAULT")
                    )
                
                # 每30秒发送一次心跳
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"心跳检测异常: {e}")
                time.sleep(10)  # 异常时等待10秒后重试
    
    def shutdown(self):
        """关闭Nacos客户端，注销所有服务"""
        logger.info("正在关闭Nacos客户端...")
        
        # 停止心跳
        self.stop_heartbeat()
        
        # 注销所有已注册的服务
        for service_key, service_info in list(self.registered_services.items()):
            self.deregister_service(
                service_name=service_info["service_name"],
                ip=service_info["ip"],
                port=service_info["port"],
                group_name=service_info.get("group_name", "DEFAULT_GROUP"),
                cluster_name=service_info.get("cluster_name", "DEFAULT")
            )
        
        logger.info("Nacos客户端已关闭")


# 全局Nacos客户端实例
nacos_client: Optional[NacosServiceRegistry] = None


def init_nacos_client(server_addresses: str = "127.0.0.1:8848", 
                     namespace: str = "", username: str = "", password: str = "") -> NacosServiceRegistry:
    """初始化全局Nacos客户端"""
    global nacos_client
    nacos_client = NacosServiceRegistry(server_addresses, namespace, username, password)
    return nacos_client


def get_nacos_client() -> Optional[NacosServiceRegistry]:
    """获取全局Nacos客户端"""
    return nacos_client
